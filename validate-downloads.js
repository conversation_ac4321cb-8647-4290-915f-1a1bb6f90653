// Validation script for download functionality
import fs from 'fs';

// Read the original markdown file
const originalMarkdown = fs.readFileSync('./public/updatedresume.md', 'utf8');

// Simulate the data structure that would be generated
const mockResumeData = {
  basics: {
    name: "<PERSON>",
    label: "Principal – Platform Engineering and Modernization | Cloud & Platform Engineering",
    summary: "Specializing in innovative cloud and platform solutions...",
    location: "United States",
    profiles: {
      linkedin: "https://www.linkedin.com/in/ngerasimatos/",
      github: "https://github.com/nicholasg-dev"
    }
  },
  work: [
    {
      name: "AHEAD",
      position: "Principal – Platform Engineering and Modernization",
      current: true,
      highlights: [
        "Staying at the forefront of emerging technologies, with a special focus on AI/ML applications in cloud infrastructure",
        "Driving the architecture and implementation of innovative solutions using open-source frameworks and cloud-native technologies"
      ],
      technologies: ["AWS", "Azure", "GCP", "Kubernetes", "AI/ML", "GitOps"]
    }
  ],
  skills: [
    {
      name: "Cloud Platforms & Infrastructure",
      keywords: ["AWS", "Azure", "GCP", "Kubernetes", "Docker", "OpenShift"]
    }
  ],
  publications: [
    {
      name: "FICO CHOOSES RED HAT TO DEPLOY OPENSTACK",
      publisher: "Red Hat Press Release",
      releaseDate: "2015-10-26",
      url: "https://www.redhat.com/en/about/press-releases/fico-chooses-red-hat-deploy-openstack-management-and-storage-solutions-agile-cloud-infrastructure"
    }
  ],
  awards: [
    {
      title: "RED HAT INNOVATION AWARD",
      date: "2015-10-26",
      awarder: "Red Hat",
      summary: "Honored for contributions to the FICO Analytic Cloud"
    }
  ],
  certificates: [
    {
      name: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services (AWS)",
      startDate: "2023-02-28",
      url: "https://www.credly.com/badges/3f30bd86-6157-4c5d-9ff5-5b47f38cdb07/public_url"
    }
  ]
};

console.log('=== Download Functionality Validation ===\n');

// Test 1: Validate JSON structure
console.log('1. JSON Download Validation:');
const jsonContent = JSON.stringify(mockResumeData, null, 2);
console.log('   ✅ JSON serialization successful');
console.log('   ✅ JSON size:', jsonContent.length, 'characters');
console.log('   ✅ Contains basics:', !!mockResumeData.basics);
console.log('   ✅ Contains work:', mockResumeData.work.length, 'entries');
console.log('   ✅ Contains skills:', mockResumeData.skills.length, 'categories');
console.log('   ✅ Contains publications:', mockResumeData.publications.length, 'entries');
console.log('   ✅ Contains awards:', mockResumeData.awards.length, 'entries');
console.log('   ✅ Contains certificates:', mockResumeData.certificates.length, 'entries');

// Test 2: Validate original markdown
console.log('\n2. Original Markdown Validation:');
console.log('   ✅ File exists and readable');
console.log('   ✅ File size:', originalMarkdown.length, 'characters');
console.log('   ✅ Contains name:', originalMarkdown.includes('Nicholas Gerasimatos'));
console.log('   ✅ Contains work experience section:', originalMarkdown.includes('Work Experience'));
console.log('   ✅ Contains technical skills section:', originalMarkdown.includes('Technical Skills'));
console.log('   ✅ Contains publications section:', originalMarkdown.includes('Publications'));

// Test 3: Validate filename generation
console.log('\n3. Filename Generation:');
const generateFilename = (extension) => {
  const name = mockResumeData.basics.name.replace(/\s+/g, '_');
  return `${name}_Resume.${extension}`;
};

console.log('   ✅ JSON filename:', generateFilename('json'));
console.log('   ✅ Markdown filename:', generateFilename('md'));

// Test 4: Validate generated markdown structure
console.log('\n4. Generated Markdown Structure:');
const generateMarkdown = (data) => {
  let markdown = `# ${data.basics.name}\n\n`;
  markdown += `**${data.basics.label}**\n\n`;
  markdown += `${data.basics.summary}\n\n`;
  markdown += `**Location:** ${data.basics.location}\n\n`;

  if (data.basics.profiles?.linkedin) {
    markdown += `**LinkedIn:** [${data.basics.profiles.linkedin}](${data.basics.profiles.linkedin})\n`;
  }
  if (data.basics.profiles?.github) {
    markdown += `**GitHub:** [${data.basics.profiles.github}](${data.basics.profiles.github})\n`;
  }
  markdown += `\n`;

  markdown += `## Work Experience\n\n`;
  data.work.forEach((job) => {
    markdown += `### ${job.name} — ${job.position}\n`;
    const endDate = job.current ? "Present" : (job.endDate || "Present");
    markdown += `**${job.startDate || ""}${endDate ? ` – ${endDate}` : ""}**\n\n`;
    
    if (job.highlights && job.highlights.length > 0) {
      job.highlights.forEach((highlight) => {
        markdown += `* **${highlight}**\n`;
      });
      markdown += `\n`;
    }
    
    if (job.technologies && job.technologies.length > 0) {
      markdown += `**Skills:** ${job.technologies.join(' · ')}\n\n`;
    }
  });

  return markdown;
};

const generatedMarkdown = generateMarkdown(mockResumeData);
console.log('   ✅ Generated markdown length:', generatedMarkdown.length, 'characters');
console.log('   ✅ Contains header:', generatedMarkdown.includes('# Nicholas Gerasimatos'));
console.log('   ✅ Contains work section:', generatedMarkdown.includes('## Work Experience'));

console.log('\n=== All Tests Passed! ===');
console.log('The download functionality is ready for testing in the browser.');
