// Final test to verify parsing works correctly
import fs from 'fs';

// Import the actual parsing function (we'll simulate it)
const markdownContent = fs.readFileSync('./public/updatedresume.md', 'utf8');

// Simulate the parsing logic from markdownDataService.ts
const lines = markdownContent.split('\n');
const result = {
  basics: {
    name: '',
    label: '',
    summary: '',
    location: 'United States',
    profiles: {
      linkedin: '',
      github: ''
    }
  },
  work: [],
  skills: [],
  publications: [],
  awards: [],
  certificates: []
};

let currentSection = '';
let inSkillsSection = false;

for (let i = 0; i < lines.length; i++) {
  const line = lines[i].trim();
  
  if (!line) continue;

  // Extract name from the first line
  if (i === 0 && line.includes('<PERSON> G<PERSON>simatos')) {
    result.basics.name = '<PERSON> Gerasimat<PERSON>';
    continue;
  }

  // Section headers - exclude publication headers
  const isPublicationHeader = ['Title', 'Publisher', 'URL'].includes(line);
  
  if (!isPublicationHeader && (line.match(/^[A-Z][^a-z]*$/) || line.match(/^[A-Z][a-zA-Z\s&/]*$/) && lines[i+1]?.match(/^-+$/))) {
    currentSection = line.toLowerCase();
    inSkillsSection = currentSection === 'technical skills';
    continue;
  }

  // Skip underline characters
  if (line.match(/^-+$/) || line.match(/^=+$/)) {
    continue;
  }

  // Technical Skills parsing
  if (inSkillsSection && line.startsWith('**') && line.includes(':**')) {
    const categoryMatch = line.match(/\*\*([^:]+):\*\*/);
    if (categoryMatch) {
      const categoryName = categoryMatch[1].trim();
      const skillsText = line.replace(/\*\*[^:]+:\*\*/, '').trim();
      
      const skills = skillsText
        .split(/[,]/)
        .map(skill => skill.trim())
        .filter(skill => skill.length > 0);
      
      if (skills.length > 0) {
        result.skills.push({
          name: categoryName,
          keywords: skills
        });
      }
    }
    continue;
  }

  // Publications parsing
  if (currentSection === 'publications') {
    if (line === 'Title' || line === 'Publisher' || line === 'URL') {
      continue;
    }
    
    if (line.startsWith('http') || line.startsWith('[http') || line.startsWith('[https')) {
      let title = '';
      let publisher = '';
      
      for (let j = i - 1; j >= 0; j--) {
        const prevLine = lines[j]?.trim();
        if (!prevLine) continue;
        
        if (prevLine === 'Title' || prevLine === 'Publisher' || prevLine === 'URL') {
          break;
        }
        
        if (!publisher) {
          publisher = prevLine;
        } else if (!title) {
          title = prevLine;
          break;
        }
      }
      
      if (title && publisher) {
        let url = line;
        const urlMatch = line.match(/\[([^\]]+)\]\(([^)]+)\)/);
        if (urlMatch) {
          url = urlMatch[2];
        }
        
        result.publications.push({
          name: title,
          publisher: publisher,
          releaseDate: '2015-01-01',
          url: url
        });
      }
      continue;
    }
  }
}

console.log('=== Final Parsing Test Results ===\n');
console.log(`Name: ${result.basics.name}`);
console.log(`Skills categories: ${result.skills.length}`);
console.log(`Publications: ${result.publications.length}`);

if (result.skills.length > 0) {
  console.log('\n✅ Skills found:');
  result.skills.forEach((skill, index) => {
    console.log(`  ${index + 1}. ${skill.name}: ${skill.keywords.length} skills`);
  });
}

if (result.publications.length > 0) {
  console.log('\n✅ Publications found:');
  result.publications.forEach((pub, index) => {
    console.log(`  ${index + 1}. "${pub.name}" by ${pub.publisher}`);
  });
} else {
  console.log('\n❌ No publications found');
}

console.log('\n=== Test Complete ===');
