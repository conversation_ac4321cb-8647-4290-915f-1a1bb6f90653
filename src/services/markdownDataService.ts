import { parseMarkdownResume, ParsedMarkdownData } from '../utils/markdownParser';

// Type definitions matching the existing resumeData structure
export interface ResumeData {
  basics: {
    name: string;
    label: string;
    summary: string;
    location: string;
    email?: string;
    profiles: {
      linkedin: string;
      github?: string;
    };
  };
  work: Array<{
    name: string;
    position: string;
    startDate?: string;
    endDate?: string;
    current?: boolean;
    highlights: string[];
    technologies: string[];
    summary?: string;
    url?: string;
  }>;
  skills: Array<{
    name: string;
    keywords: string[];
  }>;
  publications: Array<{
    name: string;
    publisher: string;
    releaseDate: string;
    url?: string;
  }>;
  awards: Array<{
    title: string;
    date: string;
    awarder: string;
    summary: string;
    url?: string;
  }>;
  certificates: Array<{
    name: string;
    issuer: string;
    startDate?: string;
    endDate?: string;
    url?: string;
  }>;
  volunteer?: Array<{
    organization: string;
    position: string;
    startDate?: string;
    current?: boolean;
    summary: string;
    highlights: string[];
  }>;
  references?: Array<{
    name: string;
    reference: string;
  }>;
}

/**
 * Enhanced markdown parser that handles the specific structure of updatedresume.md
 */
export async function parseEnhancedMarkdownResume(markdownContent: string): Promise<ResumeData> {
  const lines = markdownContent.split('\n');
  const result: ResumeData = {
    basics: {
      name: '',
      label: '',
      summary: '',
      location: 'United States',
      profiles: {
        linkedin: '',
        github: ''
      }
    },
    work: [],
    skills: [],
    publications: [],
    awards: [],
    certificates: [],
    volunteer: [],
    references: []
  };

  let currentSection = '';
  let currentWorkEntry: any = null;
  let currentSkillsText = '';
  let inSkillsSection = false;
  let inPublicationsTable = false;
  let publicationTableHeaders: string[] = [];
  let currentCertificationCategory = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Skip empty lines
    if (!line) continue;

    // Extract name from the first line
    if (i === 0 && line.includes('Nicholas Gerasimatos')) {
      result.basics.name = 'Nicholas Gerasimatos';
      continue;
    }

    // Extract label/title
    if (line.includes('Principal – Platform Engineering') || line.includes('Cloud & Platform Engineering')) {
      result.basics.label = 'Principal Technical Consultant | Cloud and Platform Engineering';
      continue;
    }

    // Extract GitHub profile
    if (line.includes('github.com/nicholas-gerasimatos')) {
      result.basics.profiles.github = 'https://github.com/nicholasg-dev';
      continue;
    }

    // Extract LinkedIn profile
    if (line.includes('linkedin.com/in/nicholas-gerasimatos')) {
      result.basics.profiles.linkedin = 'https://www.linkedin.com/in/ngerasimatos/';
      continue;
    }

    // Section headers
    if (line.match(/^[A-Z][^a-z]*$/)) {
      currentSection = line.toLowerCase();
      inSkillsSection = currentSection === 'technical skills';
      inPublicationsTable = false;
      currentCertificationCategory = '';
      
      if (currentWorkEntry && currentSection !== 'work experience') {
        result.work.push(currentWorkEntry);
        currentWorkEntry = null;
      }
      continue;
    }

    // Professional Summary
    if (currentSection === 'professional summary' && line.length > 50) {
      result.basics.summary = line;
      continue;
    }

    // Work Experience parsing
    if (currentSection === 'work experience') {
      // Company and position line (e.g., "AHEAD — Principal – Platform Engineering and Modernization")
      if (line.includes('—') && !line.startsWith('*') && !line.match(/^\d{4}/)) {
        if (currentWorkEntry) {
          result.work.push(currentWorkEntry);
        }

        const parts = line.split('—').map(p => p.trim());
        currentWorkEntry = {
          name: parts[0],
          position: parts[1] || '',
          highlights: [],
          technologies: [],
          current: false,
          url: getCompanyUrl(parts[0])
        };
        continue;
      }

      // Date range (handles both "2022 – 2024" and "May 2024 – Present" formats)
      if (line.match(/^\d{4}\s*[–-]\s*(Present|\d{4})/) || line.match(/^[A-Z][a-z]+\s+\d{4}\s*[–-]/)) {
        if (currentWorkEntry) {
          if (line.toLowerCase().includes('present')) {
            currentWorkEntry.current = true;
          }
          // Extract start date
          const dateMatch = line.match(/([A-Z][a-z]+\s+\d{4}|\d{4})/);
          if (dateMatch) {
            currentWorkEntry.startDate = dateMatch[1];
          }
        }
        continue;
      }

      // Highlights (bullet points with bold formatting)
      if (line.startsWith('*') && line.includes('**') && currentWorkEntry) {
        const highlight = line.replace(/^\*\s*/, '').replace(/\*\*/g, '');
        currentWorkEntry.highlights.push(highlight);
        continue;
      }

      // Skills line
      if (line.startsWith('**Skills:**') && currentWorkEntry) {
        const skillsText = line.replace('**Skills:**', '').trim();
        const technologies = skillsText.split('·').map(s => s.trim()).filter(s => s.length > 0);
        currentWorkEntry.technologies = technologies;
        continue;
      }
    }

    // Technical Skills parsing
    if (inSkillsSection && line.startsWith('**') && line.includes(':**')) {
      // Extract category name
      const categoryMatch = line.match(/\*\*([^:]+):\*\*/);
      if (categoryMatch) {
        const categoryName = categoryMatch[1].trim();
        const skillsText = line.replace(/\*\*[^:]+:\*\*/, '').trim();
        
        // Parse skills from the text
        const skills = skillsText
          .split(/[,·]/)
          .map(skill => skill.trim())
          .filter(skill => skill.length > 0);
        
        if (skills.length > 0) {
          result.skills.push({
            name: categoryName,
            keywords: skills
          });
        }
      }
      continue;
    }

    // Certifications parsing
    if (currentSection === 'certifications') {
      // Category headers (e.g., "AI / ML", "Amazon Web Services")
      if (line.match(/^[A-Z]/) && !line.startsWith('*') && line.length < 50) {
        currentCertificationCategory = line;
        continue;
      }

      // Individual certifications
      if (line.startsWith('*')) {
        const cert = parseCertificationLine(line);
        if (cert) {
          result.certificates.push(cert);
        }
        continue;
      }
    }

    // Awards parsing
    if (currentSection === 'awards and recognition' && line.startsWith('*')) {
      const award = parseAwardLine(line);
      if (award) {
        result.awards.push(award);
      }
      continue;
    }

    // Publications parsing
    if (currentSection === 'publications') {
      // Handle table headers
      if (line.includes('Title') && line.includes('Publisher') && line.includes('URL')) {
        inPublicationsTable = true;
        publicationTableHeaders = line.split('|').map(h => h.trim());
        continue;
      }

      // Skip table separator line
      if (line.includes('---') || line.includes('===')) {
        continue;
      }

      // Parse publication rows
      if (inPublicationsTable && line.includes('|')) {
        const publication = parsePublicationTableRow(line);
        if (publication) {
          result.publications.push(publication);
        }
        continue;
      }
    }
  }

  // Add the last work entry if it exists
  if (currentWorkEntry) {
    result.work.push(currentWorkEntry);
  }

  // Add volunteer experience
  result.volunteer = [{
    organization: 'Humane Society',
    position: 'Volunteer',
    startDate: '2010-01-01',
    current: true,
    summary: 'Volunteer work supporting animal welfare and community outreach programs.',
    highlights: [
      'Supported animal welfare initiatives and community outreach programs',
      'Contributed to fundraising and awareness campaigns',
      'Assisted with animal care and adoption events'
    ]
  }];

  return result;
}

/**
 * Get company URL based on company name
 */
function getCompanyUrl(companyName: string): string {
  const urlMap: { [key: string]: string } = {
    'AHEAD': 'https://www.thinkahead.com/',
    'Amazon Web Services': 'https://aws.amazon.com/',
    'Red Hat': 'https://www.redhat.com/',
    'FICO': 'https://www.fico.com/',
    'American Express': 'https://www.linkedin.com/company/american-express/',
    'VCE': 'https://www.linkedin.com/company/vce/',
    'Microsoft': 'https://www.linkedin.com/company/microsoft/'
  };
  
  return urlMap[companyName] || '';
}

/**
 * Parse a certification line
 */
function parseCertificationLine(line: string): any {
  // Handle linked certifications
  const linkedMatch = line.match(/\*\s*\[([^\]]+)\]\(([^)]+)\)\s*[–-]\s*([^,]+)(?:,\s*issued\s*(.+))?/);
  if (linkedMatch) {
    return {
      name: linkedMatch[1],
      issuer: linkedMatch[3],
      startDate: linkedMatch[4] || '',
      url: linkedMatch[2]
    };
  }

  // Handle simple certifications
  const simpleMatch = line.match(/\*\s*([^–-]+)(?:[–-]\s*(.+))?/);
  if (simpleMatch) {
    return {
      name: simpleMatch[1].trim(),
      issuer: simpleMatch[2]?.trim() || '',
      startDate: '',
      url: ''
    };
  }

  return null;
}

/**
 * Parse an award line
 */
function parseAwardLine(line: string): any {
  const match = line.match(/\*\s*\*\*([^*]+)\*\*\s*[–-]\s*(.+)/);
  if (match) {
    return {
      title: match[1].trim(),
      date: '2015-10-26',
      awarder: 'Red Hat',
      summary: match[2].trim(),
      url: 'http://www.marketwatch.com/story/fico-chooses-red-hat-to-deploy-openstack-management-and-storage-solutions-for-agile-cloud-infrastructure-2015-10-26'
    };
  }
  return null;
}

/**
 * Parse a publication table row
 */
function parsePublicationTableRow(line: string): any {
  const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell.length > 0);
  
  if (cells.length >= 3) {
    const publication = {
      name: cells[0],
      publisher: cells[1],
      releaseDate: '2015-01-01', // Default date
      url: ''
    };

    // Extract URL from markdown link in the third cell
    if (cells[2]) {
      const urlMatch = cells[2].match(/\[.*?\]\(([^)]+)\)/);
      if (urlMatch) {
        publication.url = urlMatch[1];
      }
    }

    return publication;
  }

  return null;
}

/**
 * Load and parse markdown resume from file
 */
export async function loadMarkdownResume(): Promise<ResumeData> {
  try {
    const response = await fetch('/updatedresume.md');
    if (!response.ok) {
      throw new Error(`Failed to load resume: ${response.statusText}`);
    }
    
    const markdownContent = await response.text();
    return await parseEnhancedMarkdownResume(markdownContent);
  } catch (error) {
    console.error('Error loading markdown resume:', error);
    throw error;
  }
}
