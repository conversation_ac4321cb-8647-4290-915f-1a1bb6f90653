import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ExternalLink, Github, Folder } from "lucide-react";

const FeaturedProjects = () => {
  const projects = [
    {
      id: 1,
      title: "Enterprise Cloud-Native Platform",
      description:
        "A comprehensive cloud-native platform built on OpenShift and AWS, enabling enterprises to modernize applications with containerization and microservices architecture. This platform enables Fortune 500 companies to accelerate their digital transformation journey.",
      image:
        "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=800&h=600&fit=crop",
      technologies: [
        "OpenShift",
        "AWS",
        "Kubernetes",
        "ArgoCD",
        "Istio",
        "Terraform",
        "Tekton",
        "Prometheus",
        "Grafana",
        "OpenTelemetry",
      ],
      category: "Cloud Architecture",
      year: "2024",
      duration: "8 months",
      role: "Principal Architect",
      teamSize: "10 engineers",
      client: "Fortune 500 Financial Services",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 2,
      title: "AI/ML Model Serving Platform",
      description:
        "A production-grade platform for deploying, managing, and monitoring machine learning models at scale with MLOps best practices. Enables healthcare providers to deploy AI models for patient care optimization.",
      image:
        "https://images.unsplash.com/photo-1518770660439-4636190af475?w=800&h=600&fit=crop",
      technologies: [
        "Kubeflow",
        "AWS Bedrock",
        "AWS SageMaker",
        "MLflow",
        "KServe",
        "Prometheus",
        "Grafana",
        "TensorFlow",
        "PyTorch",
        "ONNX",
        "Python",
        "Docker",
      ],
      category: "AI/ML",
      year: "2023",
      duration: "6 months",
      role: "Lead Platform Architect",
      teamSize: "6 engineers",
      client: "Leading Healthcare Provider",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 3,
      title: "Hybrid Cloud Data Fabric",
      description:
        "A unified data access layer enabling seamless data movement and analytics across on-premises and multi-cloud environments. Provides real-time insights for global retail operations.",
      image:
        "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop",
      technologies: [
        "Red Hat OpenShift Data Science",
        "Apache Kafka",
        "Debezium",
        "Apache Spark",
        "Presto",
        "Apache Atlas",
        "ThoughtSpot",
        "AWS S3",
        "Azure Data Lake",
        "Apache NiFi",
        "Delta Lake",
      ],
      category: "Data Architecture",
      year: "2023",
      duration: "9 months",
      role: "Principal Data Architect",
      teamSize: "8 engineers",
      client: "Global Retail Enterprise",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 4,
      title: "Zero-Trust Security Framework",
      description:
        "A comprehensive zero-trust security implementation for cloud-native applications with continuous verification and least-privilege access. Provides enterprise-grade security for sensitive workloads.",
      image:
        "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=600&fit=crop",
      technologies: [
        "Istio",
        "SPIFFE/SPIRE",
        "Open Policy Agent",
        "Vault",
        "Keycloak",
        "mTLS",
        "OAuth2",
        "OpenID Connect",
        "Falco",
        "Trivy",
      ],
      category: "Security",
      year: "2024",
      duration: "5 months",
      role: "Security Architect",
      teamSize: "4 engineers",
      client: "Government Agency",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 5,
      title: "Cloud FinOps Optimization Platform",
      description:
        "An intelligent cost optimization platform leveraging AI to analyze cloud spending patterns and provide automated recommendations for cost reduction across multi-cloud environments.",
      image:
        "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop",
      technologies: [
        "AWS Cost Explorer API",
        "AWS Lambda",
        "EventBridge",
        "Kinesis",
        "Azure Cost Management",
        "GCP Billing API",
        "Python",
        "TensorFlow",
        "React",
        "D3.js",
        "PostgreSQL",
      ],
      category: "Cloud FinOps",
      year: "2024",
      duration: "4 months",
      role: "Cloud Architect",
      teamSize: "3 engineers",
      client: "Enterprise SaaS Company",
      liveUrl: "#",
      githubUrl: "#",
    },
  ];

  return (
    <section className="py-16 px-6">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 mb-4 text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
            <Folder className="h-4 w-4" />
            Featured Work
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Projects That Define My Expertise
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            A showcase of my best work, demonstrating technical skills and
            creative problem-solving across cloud platforms, AI/ML, and
            enterprise architecture.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {projects.map((project) => (
            <Card
              key={project.id}
              className="overflow-hidden hover:shadow-lg transition-shadow duration-300 group"
            >
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <Badge className="bg-blue-600 text-white">
                    {project.category}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4 flex space-x-2">
                  <Button
                    size="icon"
                    variant="secondary"
                    className="w-8 h-8 bg-white/90 backdrop-blur-sm hover:bg-blue-50"
                    asChild
                  >
                    <a
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="View live demo"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                  <Button
                    size="icon"
                    variant="secondary"
                    className="w-8 h-8 bg-white/90 backdrop-blur-sm hover:bg-blue-50"
                    asChild
                  >
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="View source code"
                    >
                      <Github className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {project.title}
                  </h3>
                  <span className="text-sm text-gray-500">{project.year}</span>
                </div>

                <p className="text-gray-600 mb-4 leading-relaxed">
                  {project.description}
                </p>

                {/* Project metadata */}
                <div className="grid grid-cols-2 gap-2 mb-4 text-xs text-gray-500">
                  <div>Role: {project.role}</div>
                  <div>Duration: {project.duration}</div>
                  <div>Team: {project.teamSize}</div>
                  <div>Client: {project.client}</div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.slice(0, 6).map((tech, techIndex) => (
                    <Badge
                      key={techIndex}
                      variant="outline"
                      className="text-xs"
                    >
                      {tech}
                    </Badge>
                  ))}
                  {project.technologies.length > 6 && (
                    <Badge variant="outline" className="text-xs">
                      +{project.technologies.length - 6} more
                    </Badge>
                  )}
                </div>

                <div className="pt-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600 hover:text-blue-700"
                  >
                    View Details
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
            <Folder className="h-5 w-5 mr-2" />
            View All Projects
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjects;
