import React from 'react';
import { useResumeData } from '@/hooks/useMarkdownResume';

const DebugData = () => {
  const { data, loading, error } = useResumeData();

  if (loading) return <div>Loading...</div>;

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-2">Debug Data</h3>
      {error && <div className="text-red-600 mb-2">Error: {error}</div>}
      <div className="text-sm">
        <p><strong>Name:</strong> {data.basics.name}</p>
        <p><strong>Label:</strong> {data.basics.label}</p>
        <p><strong>Work Entries:</strong> {data.work.length}</p>
        <p><strong>Skills Categories:</strong> {data.skills.length}</p>
        <p><strong>Publications:</strong> {data.publications.length}</p>
        <p><strong>Awards:</strong> {data.awards.length}</p>
        <p><strong>Certificates:</strong> {data.certificates.length}</p>
        
        {data.work.length > 0 && (
          <div className="mt-2">
            <strong>First Work Entry:</strong>
            <pre className="text-xs bg-white p-2 rounded mt-1">
              {JSON.stringify(data.work[0], null, 2)}
            </pre>
          </div>
        )}
        
        {data.skills.length > 0 && (
          <div className="mt-2">
            <strong>First Skill Category:</strong>
            <pre className="text-xs bg-white p-2 rounded mt-1">
              {JSON.stringify(data.skills[0], null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default DebugData;
