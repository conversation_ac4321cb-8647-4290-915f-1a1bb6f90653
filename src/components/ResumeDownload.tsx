import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Download, FileText, File, ChevronDown } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ResumeData {
  basics: {
    name: string;
    label: string;
    summary: string;
    location: string;
    profiles: {
      linkedin: string;
      github?: string;
    };
  };
  work: Array<{
    position: string;
    name: string;
    startDate?: string;
    endDate?: string;
    summary: string;
    highlights?: string[];
  }>;
  skills: Array<{
    name: string;
    keywords: string[];
  }>;
  publications?: Array<{
    name: string;
    publisher: string;
    releaseDate: string;
    url?: string;
  }>;
  certificates?: Array<{
    name: string;
    issuer: string;
  }>;
}

interface ResumeDownloadProps {
  resumeData: ResumeData;
}

const ResumeDownload = ({ resumeData }: ResumeDownloadProps) => {
  const [selectedFormat, setSelectedFormat] = useState<string>("pdf");
  const { toast } = useToast();

  const generateMarkdown = (data: ResumeData) => {
    let markdown = `# ${data.basics.name}\n\n`;
    markdown += `**${data.basics.label}**\n\n`;
    markdown += `${data.basics.summary}\n\n`;
    markdown += `**Location:** ${data.basics.location}\n\n`;

    if (data.basics.profiles?.linkedin) {
      markdown += `**LinkedIn:** [${data.basics.profiles.linkedin}](${data.basics.profiles.linkedin})\n\n`;
    }

    markdown += `## Work Experience\n\n`;
    data.work.forEach((job: ResumeData['work'][number]) => {
      markdown += `### ${job.position} at ${job.name}\n`;
      markdown += `**${job.startDate || ""}${job.endDate ? ` - ${job.endDate}` : " - Present"}**\n\n`; // Added fallback for startDate
      markdown += `${job.summary}\n\n`;
      if (job.highlights && job.highlights.length > 0) {
        markdown += `**Key Achievements:**\n`;
        job.highlights.forEach((highlight: string) => {
          markdown += `- ${highlight}\n`;
        });
        markdown += `\n`;
      }
    });

    markdown += `## Skills\n\n`;
    data.skills.forEach((skillGroup: ResumeData['skills'][number]) => {
      markdown += `### ${skillGroup.name}\n`;
      markdown += `${skillGroup.keywords.join(", ")}\n\n`;
    });

    if (data.publications && data.publications.length > 0) {
      markdown += `## Publications\n\n`;
      data.publications.forEach((pub: NonNullable<ResumeData['publications']>[number]) => {
        markdown += `### ${pub.name}\n`;
        markdown += `**${pub.publisher}** - ${pub.releaseDate}\n`;
        if (pub.url) {
          markdown += `[Read Article](${pub.url})\n`;
        }
        markdown += `\n`;
      });
    }

    if (data.certificates && data.certificates.length > 0) {
      markdown += `## Certifications\n\n`;
      data.certificates.forEach((cert: NonNullable<ResumeData['certificates']>[number]) => {
        markdown += `- **${cert.name}** - ${cert.issuer}\n`;
      });
    }

    return markdown;
  };

  const downloadFile = (content: string, filename: string, type: string) => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleDownload = () => {
    switch (selectedFormat) {
      case "json":
        downloadFile(
          JSON.stringify(resumeData, null, 2),
          "nicholas-gerasimatos-resume.json",
          "application/json",
        );
        toast({
          title: "Resume Downloaded",
          description: "JSON format downloaded successfully",
        });
        break;

      case "markdown": {
        const markdownContent = generateMarkdown(resumeData);
        downloadFile(
          markdownContent,
          "nicholas-gerasimatos-resume.md",
          "text/markdown",
        );
        toast({
          title: "Resume Downloaded",
          description: "Markdown format downloaded successfully",
        });
        break;
      }

      case "pdf":
        // For PDF, we'll show a message that it's not implemented yet
        toast({
          title: "PDF Download",
          description:
            "PDF generation will be implemented soon. Please use JSON or Markdown format for now.",
          variant: "destructive",
        });
        break;

      default:
        break;
    }
  };

  const handleFormatSelect = (format: string) => {
    setSelectedFormat(format);
    handleDownload();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Download Resume
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleFormatSelect("pdf")}>
          <FileText className="h-4 w-4 mr-2" />
          PDF
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleFormatSelect("json")}>
          <File className="h-4 w-4 mr-2" />
          JSON
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleFormatSelect("markdown")}>
          <FileText className="h-4 w-4 mr-2" />
          Markdown
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ResumeDownload;
