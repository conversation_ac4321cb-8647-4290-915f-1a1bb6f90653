import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Download, FileText, File, ChevronDown } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ResumeData {
  basics: {
    name: string;
    label: string;
    summary: string;
    location: string;
    email?: string;
    profiles: {
      linkedin: string;
      github?: string;
    };
  };
  work: Array<{
    name: string;
    position: string;
    startDate?: string;
    endDate?: string;
    current?: boolean;
    highlights: string[];
    technologies: string[];
    summary?: string;
    url?: string;
  }>;
  skills: Array<{
    name: string;
    keywords: string[];
  }>;
  publications: Array<{
    name: string;
    publisher: string;
    releaseDate: string;
    url?: string;
  }>;
  awards: Array<{
    title: string;
    date: string;
    awarder: string;
    summary: string;
    url?: string;
  }>;
  certificates: Array<{
    name: string;
    issuer: string;
    startDate?: string;
    endDate?: string;
    url?: string;
  }>;
  volunteer?: Array<{
    organization: string;
    position: string;
    startDate?: string;
    current?: boolean;
    summary: string;
    highlights: string[];
  }>;
  references?: Array<{
    name: string;
    reference: string;
  }>;
}

interface ResumeDownloadProps {
  resumeData: ResumeData;
}

const ResumeDownload = ({ resumeData }: ResumeDownloadProps) => {
  const [selectedFormat, setSelectedFormat] = useState<string>("pdf");
  const { toast } = useToast();

  const generateMarkdown = (data: ResumeData) => {
    let markdown = `# ${data.basics.name}\n\n`;
    markdown += `**${data.basics.label}**\n\n`;
    markdown += `${data.basics.summary}\n\n`;
    markdown += `**Location:** ${data.basics.location}\n\n`;

    if (data.basics.profiles?.linkedin) {
      markdown += `**LinkedIn:** [${data.basics.profiles.linkedin}](${data.basics.profiles.linkedin})\n`;
    }
    if (data.basics.profiles?.github) {
      markdown += `**GitHub:** [${data.basics.profiles.github}](${data.basics.profiles.github})\n`;
    }
    markdown += `\n`;

    markdown += `## Work Experience\n\n`;
    data.work.forEach((job: ResumeData['work'][number]) => {
      markdown += `### ${job.name} — ${job.position}\n`;
      const endDate = job.current ? "Present" : (job.endDate || "Present");
      markdown += `**${job.startDate || ""}${endDate ? ` – ${endDate}` : ""}**\n\n`;

      if (job.summary) {
        markdown += `${job.summary}\n\n`;
      }

      if (job.highlights && job.highlights.length > 0) {
        job.highlights.forEach((highlight: string) => {
          markdown += `* **${highlight}**\n`;
        });
        markdown += `\n`;
      }

      if (job.technologies && job.technologies.length > 0) {
        markdown += `**Skills:** ${job.technologies.join(' · ')}\n\n`;
      }
    });

    markdown += `## Technical Skills\n\n`;
    data.skills.forEach((skillGroup: ResumeData['skills'][number]) => {
      markdown += `**${skillGroup.name}:** ${skillGroup.keywords.join(', ')}\n\n`;
    });

    if (data.publications && data.publications.length > 0) {
      markdown += `## Publications\n\n`;
      markdown += `| Title | Publisher | URL |\n`;
      markdown += `|-------|-----------|-----|\n`;
      data.publications.forEach((pub: ResumeData['publications'][number]) => {
        const urlCell = pub.url ? `[Link](${pub.url})` : '';
        markdown += `| ${pub.name} | ${pub.publisher} | ${urlCell} |\n`;
      });
      markdown += `\n`;
    }

    if (data.certificates && data.certificates.length > 0) {
      markdown += `## Certifications\n\n`;
      data.certificates.forEach((cert: ResumeData['certificates'][number]) => {
        const certLine = cert.url ?
          `* [${cert.name}](${cert.url}) – ${cert.issuer}` :
          `* ${cert.name} – ${cert.issuer}`;
        if (cert.startDate) {
          markdown += `${certLine}, issued ${cert.startDate}\n`;
        } else {
          markdown += `${certLine}\n`;
        }
      });
      markdown += `\n`;
    }

    if (data.awards && data.awards.length > 0) {
      markdown += `## Awards and Recognition\n\n`;
      data.awards.forEach((award: ResumeData['awards'][number]) => {
        markdown += `* **${award.title}** – ${award.summary}\n`;
      });
      markdown += `\n`;
    }

    return markdown;
  };

  const downloadFile = (content: string, filename: string, type: string) => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const generateFilename = (extension: string) => {
    const name = resumeData.basics.name.replace(/\s+/g, '_');
    return `${name}_Resume.${extension}`;
  };

  const handleDownload = () => {
    switch (selectedFormat) {
      case "json":
        downloadFile(
          JSON.stringify(resumeData, null, 2),
          generateFilename("json"),
          "application/json",
        );
        toast({
          title: "Resume Downloaded",
          description: "JSON format downloaded successfully",
        });
        break;

      case "markdown": {
        const markdownContent = generateMarkdown(resumeData);
        downloadFile(
          markdownContent,
          generateFilename("md"),
          "text/markdown",
        );
        toast({
          title: "Resume Downloaded",
          description: "Markdown format downloaded successfully",
        });
        break;
      }

      case "original": {
        // Download the original markdown file
        fetch('/updatedresume.md')
          .then(response => response.text())
          .then(content => {
            downloadFile(
              content,
              generateFilename("md"),
              "text/markdown",
            );
            toast({
              title: "Original Resume Downloaded",
              description: "Original markdown file downloaded successfully",
            });
          })
          .catch(error => {
            console.error('Error downloading original markdown:', error);
            toast({
              title: "Download Error",
              description: "Failed to download original markdown file",
              variant: "destructive",
            });
          });
        break;
      }

      case "pdf":
        // For PDF, we'll show a message that it's not implemented yet
        toast({
          title: "PDF Download",
          description:
            "PDF generation will be implemented soon. Please use JSON or Markdown format for now.",
          variant: "destructive",
        });
        break;

      default:
        break;
    }
  };

  const handleFormatSelect = (format: string) => {
    setSelectedFormat(format);
    handleDownload();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Download Resume
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleFormatSelect("pdf")}>
          <FileText className="h-4 w-4 mr-2" />
          PDF
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleFormatSelect("json")}>
          <File className="h-4 w-4 mr-2" />
          JSON
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleFormatSelect("markdown")}>
          <FileText className="h-4 w-4 mr-2" />
          Markdown (Generated)
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleFormatSelect("original")}>
          <FileText className="h-4 w-4 mr-2" />
          Markdown (Original)
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ResumeDownload;
