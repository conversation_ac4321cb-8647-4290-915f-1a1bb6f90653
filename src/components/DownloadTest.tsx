import React from 'react';
import { useResumeData } from '@/hooks/useMarkdownResume';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

const DownloadTest = () => {
  const { data: resumeData } = useResumeData();

  const testJsonDownload = () => {
    const jsonContent = JSON.stringify(resumeData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'Nicholas_Gerasimatos_Resume.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    console.log('JSON download test completed');
  };

  const testMarkdownDownload = async () => {
    try {
      const response = await fetch('/updatedresume.md');
      const content = await response.text();
      const blob = new Blob([content], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'Nicholas_Gerasimatos_Resume.md';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('Markdown download test completed');
    } catch (error) {
      console.error('Markdown download test failed:', error);
    }
  };

  const validateData = () => {
    console.log('=== Resume Data Validation ===');
    console.log('Name:', resumeData?.basics?.name);
    console.log('Work entries:', resumeData?.work?.length);
    console.log('Skills categories:', resumeData?.skills?.length);
    console.log('Publications:', resumeData?.publications?.length);
    console.log('Awards:', resumeData?.awards?.length);
    console.log('Certificates:', resumeData?.certificates?.length);
    console.log('Full data structure:', resumeData);
  };

  return (
    <div className="fixed top-4 left-4 bg-white border rounded-lg shadow-lg p-4 space-y-2 z-50">
      <h3 className="font-semibold text-sm">Download Tests</h3>
      <div className="space-y-2">
        <Button size="sm" onClick={testJsonDownload} className="w-full">
          <Download className="h-3 w-3 mr-1" />
          Test JSON
        </Button>
        <Button size="sm" onClick={testMarkdownDownload} className="w-full">
          <Download className="h-3 w-3 mr-1" />
          Test MD
        </Button>
        <Button size="sm" onClick={validateData} variant="outline" className="w-full">
          Validate Data
        </Button>
      </div>
    </div>
  );
};

export default DownloadTest;
