import React from 'react';
import { useResumeData } from '@/hooks/useMarkdownResume';

const DataStatus = () => {
  const { data, loading, error } = useResumeData();

  return (
    <div className="fixed bottom-4 right-4 bg-white border rounded-lg shadow-lg p-3 text-xs max-w-xs">
      <div className="font-semibold mb-1">Data Status</div>
      <div className="space-y-1">
        <div>Loading: {loading ? '✅' : '❌'}</div>
        <div>Error: {error ? '⚠️' : '✅'}</div>
        <div>Name: {data?.basics?.name || 'Not loaded'}</div>
        <div>Work entries: {data?.work?.length || 0}</div>
        <div>Skills: {data?.skills?.length || 0}</div>
        <div>Publications: {data?.publications?.length || 0}</div>
        {error && <div className="text-red-600 text-xs mt-1">{error}</div>}
      </div>
    </div>
  );
};

export default DataStatus;
