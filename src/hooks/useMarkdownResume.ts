import { useState, useEffect, useCallback } from 'react';
import { loadMarkdownResume, ResumeData } from '../services/markdownDataService';

interface UseMarkdownResumeState {
  data: ResumeData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Cache for the loaded resume data
let cachedResumeData: ResumeData | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Hook for loading and caching markdown resume data
 */
export function useMarkdownResume(): UseMarkdownResumeState {
  const [data, setData] = useState<ResumeData | null>(cachedResumeData);
  const [loading, setLoading] = useState<boolean>(!cachedResumeData);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    // Check if we have valid cached data
    const now = Date.now();
    if (cachedResumeData && cacheTimestamp && (now - cacheTimestamp) < CACHE_DURATION) {
      setData(cachedResumeData);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const resumeData = await loadMarkdownResume();
      
      // Update cache
      cachedResumeData = resumeData;
      cacheTimestamp = now;
      
      setData(resumeData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load resume data';
      setError(errorMessage);
      console.error('Error loading markdown resume:', err);
      
      // If we have cached data, use it as fallback
      if (cachedResumeData) {
        setData(cachedResumeData);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    // Clear cache and reload
    cachedResumeData = null;
    cacheTimestamp = null;
    await loadData();
  }, [loadData]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    loading,
    error,
    refetch
  };
}

/**
 * Fallback resume data in case markdown loading fails
 */
export const fallbackResumeData: ResumeData = {
  basics: {
    name: "Nicholas Gerasimatos",
    label: "Principal Technical Consultant | Cloud and Platform Engineering",
    summary: "Specializing in innovative cloud and platform solutions, I align technology with client goals to drive impactful results. With deep expertise in open-source and cloud technologies, I communicate complex systems in a clear, accessible manner and prioritize a client-centered approach.",
    location: "United States",
    email: "<EMAIL>",
    profiles: {
      linkedin: "https://www.linkedin.com/in/ngerasimatos/",
      github: "https://github.com/nicholasg-dev",
    },
  },
  work: [
    {
      name: "AHEAD",
      position: "Principal - Platform Engineering and Modernization",
      current: true,
      highlights: [
        "Staying at the forefront of emerging technologies, with a special focus on AI/ML applications in cloud infrastructure",
        "Driving the architecture and implementation of innovative solutions using open-source frameworks and cloud-native technologies",
        "Transforming system capabilities through the integration of advanced AI/ML, enabling intelligent automation and workflow optimization"
      ],
      technologies: ["AWS", "Azure", "GCP", "Kubernetes", "AI/ML", "GitOps"],
      url: "https://www.thinkahead.com/",
    },
    {
      name: "Amazon Web Services (AWS)",
      position: "Partner Cloud Architect",
      current: false,
      highlights: [
        "Designed, implemented, and optimized scalable, secure, and cost-effective AWS cloud solutions",
        "Leveraged ROSA/OpenShift, AWS Bedrock, and various other AWS services to deliver intelligent, automated solutions",
        "Led training sessions and workshops for internal teams and clients"
      ],
      technologies: ["AWS", "OpenShift", "Bedrock", "EKS", "Lambda", "S3"],
      url: "https://aws.amazon.com/",
    }
  ],
  skills: [
    {
      name: "Cloud Platforms & Infrastructure",
      keywords: ["AWS", "Azure", "GCP", "Kubernetes", "Docker", "OpenShift", "SUSE Rancher"]
    },
    {
      name: "AI/ML & Emerging Technologies",
      keywords: ["Machine Learning", "Artificial Intelligence", "MLOps", "TensorFlow", "PyTorch", "AWS Bedrock"]
    },
    {
      name: "DevOps & Cloud Native",
      keywords: ["Kubernetes", "Docker", "Helm", "GitOps", "CI/CD", "ArgoCD", "Terraform"]
    }
  ],
  publications: [
    {
      name: "FICO CHOOSES RED HAT TO DEPLOY OPENSTACK, MANAGEMENT, AND STORAGE SOLUTIONS",
      publisher: "Red Hat Press Release",
      releaseDate: "2015-10-26",
      url: "https://www.redhat.com/en/about/press-releases/fico-chooses-red-hat-deploy-openstack-management-and-storage-solutions-agile-cloud-infrastructure"
    }
  ],
  awards: [
    {
      title: "RED HAT INNOVATION AWARD",
      date: "2015-10-26",
      awarder: "Red Hat",
      summary: "Honored for contributions to the FICO Analytic Cloud, which achieved a 70% faster time-to-market and generated over US $10 million in new revenue.",
      url: "http://www.marketwatch.com/story/fico-chooses-red-hat-to-deploy-openstack-management-and-storage-solutions-for-agile-cloud-infrastructure-2015-10-26"
    }
  ],
  certificates: [
    {
      name: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services (AWS)",
      startDate: "2023-02-28",
      endDate: "2025-02-28",
      url: "https://www.credly.com/badges/3f30bd86-6157-4c5d-9ff5-5b47f38cdb07/public_url"
    }
  ],
  volunteer: [],
  references: []
};

/**
 * Hook that provides resume data with automatic fallback
 */
export function useResumeData(): UseMarkdownResumeState & { data: ResumeData } {
  const { data, loading, error, refetch } = useMarkdownResume();
  
  return {
    data: data || fallbackResumeData,
    loading,
    error,
    refetch
  };
}
