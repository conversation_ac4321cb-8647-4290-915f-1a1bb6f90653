import { useState, useEffect, useCallback } from 'react';
import { loadMarkdownResume, ResumeData } from '../services/markdownDataService';

interface UseMarkdownResumeState {
  data: ResumeData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Cache for the loaded resume data
let cachedResumeData: ResumeData | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Hook for loading and caching markdown resume data
 */
export function useMarkdownResume(): UseMarkdownResumeState {
  const [data, setData] = useState<ResumeData | null>(cachedResumeData);
  const [loading, setLoading] = useState<boolean>(!cachedResumeData);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    // Check if we have valid cached data
    const now = Date.now();
    if (cachedResumeData && cacheTimestamp && (now - cacheTimestamp) < CACHE_DURATION) {
      setData(cachedResumeData);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const resumeData = await loadMarkdownResume();
      
      // Update cache
      cachedResumeData = resumeData;
      cacheTimestamp = now;
      
      setData(resumeData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load resume data';
      setError(errorMessage);
      console.error('Error loading markdown resume:', err);
      
      // If we have cached data, use it as fallback
      if (cachedResumeData) {
        setData(cachedResumeData);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    // Clear cache and reload
    cachedResumeData = null;
    cacheTimestamp = null;
    await loadData();
  }, [loadData]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    loading,
    error,
    refetch
  };
}

/**
 * Fallback resume data in case markdown loading fails
 */
export const fallbackResumeData: ResumeData = {
  basics: {
    name: "Nicholas Gerasimatos",
    label: "Principal Technical Consultant | Cloud and Platform Engineering",
    summary: "Specializing in innovative cloud and platform solutions, I align technology with client goals to drive impactful results. With deep expertise in open-source and cloud technologies, I communicate complex systems in a clear, accessible manner and prioritize a client-centered approach.",
    location: "United States",
    email: "<EMAIL>",
    profiles: {
      linkedin: "https://www.linkedin.com/in/ngerasimatos/",
      github: "https://github.com/nicholasg-dev",
    },
  },
  work: [
    {
      name: "AHEAD",
      position: "Principal - Platform Engineering and Modernization",
      current: true,
      highlights: [
        "Staying at the forefront of emerging technologies, with a special focus on AI/ML applications in cloud infrastructure",
        "Driving the architecture and implementation of innovative solutions using open-source frameworks and cloud-native technologies",
        "Transforming system capabilities through the integration of advanced AI/ML, enabling intelligent automation and workflow optimization"
      ],
      technologies: ["AWS", "Azure", "GCP", "Kubernetes", "AI/ML", "GitOps"],
      url: "https://www.thinkahead.com/",
    }
  ],
  skills: [
    {
      name: "Cloud Platforms & Infrastructure",
      keywords: ["AWS", "Azure", "GCP", "Kubernetes", "Docker", "OpenShift"]
    },
    {
      name: "AI/ML & Emerging Technologies",
      keywords: ["Machine Learning", "Artificial Intelligence", "MLOps", "TensorFlow", "PyTorch"]
    }
  ],
  publications: [],
  awards: [],
  certificates: [],
  volunteer: [],
  references: []
};

/**
 * Hook that provides resume data with automatic fallback
 */
export function useResumeData(): UseMarkdownResumeState & { data: ResumeData } {
  const { data, loading, error, refetch } = useMarkdownResume();
  
  return {
    data: data || fallbackResumeData,
    loading,
    error,
    refetch
  };
}
