import { useState } from "react";
// import Image from "next/image"; // Unused, and this is not a Next.js project
import { <PERSON><PERSON> } from "@/components/ui/button";
import { /* Mail, */ Linkedin, Github } from "lucide-react"; // Mail is unused
import FeaturedProjects from "@/components/FeaturedProjects";
import HeroSection from "./HeroSection";
import ExperienceSection from "./ExperienceSection";
import SkillsSection from "./SkillsSection";
import PublicationsSection from "./PublicationsSection";
import NavigationTabs from "./NavigationTabs";
import ResumeDownload from "@/components/ResumeDownload";

const Index = () => {
  const [activeSection, setActiveSection] = useState("experience");

  const resumeData = {
    basics: {
      name: "<PERSON>erasimat<PERSON>",
      label: "Principal Technical Consultant | Cloud and Platform Engineering",
      summary:
        "Specializing in innovative cloud and platform solutions, I align technology with client goals to drive impactful results. With deep expertise in open-source and cloud technologies, I communicate complex systems in a clear, accessible manner and prioritize a client-centered approach. I bring a proven track record in designing and implementing scalable, secure, resilient solutions, leading modernization initiatives, and developing actionable architectures that achieve measurable business outcomes.",
      location: "United States",
      email: "<EMAIL>",
      profiles: {
        linkedin: "https://www.linkedin.com/in/ngerasimatos/",
        github: "https://github.com/nicholasg-dev",
      },
    },
    work: [
      {
        name: "AHEAD",
        position: "Principal - Platform Engineering and Modernization",
        current: true,
        highlights: [
          "Staying at the forefront of emerging technologies, with a special focus on AI/ML applications in cloud infrastructure",
          "Driving the architecture and implementation of innovative solutions using open-source frameworks and cloud-native technologies (IaC, PaaS, IaaS)",
          "Transforming system capabilities through the integration of advanced AI/ML, enabling intelligent automation and workflow optimization",
          "Elevating enterprise cloud and hybrid architectures for peak performance and scalability, with specialized expertise in Microsoft Azure integrations",
          "Orchestrating cross-functional teams, implementing agile methodologies and GitOps practices to accelerate delivery",
          "Bridging technical and business domains by translating complex concepts into clear documentation and presentations that secure executive buy-in",
          "Cultivating a culture of excellence by mentoring teams and championing industry best practices",
        ],
        summary:
          "Lead platform engineering and modernization initiatives, focusing on cloud-native solutions and AI/ML integration to drive business value and technical excellence.",
        url: "https://www.thinkahead.com/",
        technologies: [
          // Cloud Platforms
          "AWS",
          "Azure",
          "GCP",
          "OpenShift",
          "SUSE Rancher",

          // Containerization & Orchestration
          "Docker",
          "Kubernetes",
          "Helm",
          "Istio",
          "Linkerd",
          "K3s",
          "Rancher",
          "RKE",
          "RKE2",

          // Infrastructure as Code
          "Terraform",
          "Pulumi",
          "Crossplane",
          "Ansible",
          "SUSE Rancher Manager",

          // CI/CD & DevOps
          "GitHub Actions",
          "ArgoCD",
          "Jenkins",
          "Tekton",
          "GitOps",
          "DevSecOps",
          "SUSE Rancher Fleet",

          // Monitoring & Observability
          "Prometheus",
          "Grafana",
          "ELK Stack",
          "OpenTelemetry",
          "SUSE Rancher Monitoring",

          // Security
          "OPA",
          "HashiCorp Vault",
          "Falco",
          "Aqua Security",
          "NeuVector",
          "SUSE NeuVector",

          // AI/ML Ops
          "AI/ML",
          "MLflow",
          "Kubeflow",
          "Seldon Core",
          "Ray",

          // Service Mesh
          "Istio",
          "Linkerd",
          "Consul",

          // Database Technologies
          "PostgreSQL",
          "MongoDB",
          "Redis",
          "Cassandra",

          // Programming/Scripting
          "Python",
          "Go",
          "TypeScript",
          "Node.js",
          "Bash",

          // Networking
          "Istio",
          "Envoy",
          "NGINX",

          // Edge Computing
          "K3s",
          "OpenYurt",
          "EdgeX Foundry",

          // Cloud Concepts
          "PaaS",
          "IaaS",
          "Cloud-Native",
          "Multi-Cloud",
          "Serverless",

          // Methodologies
          "Agile",
          "GitOps",
          "DevSecOps",
        ],
      },
      {
        name: "Amazon Web Services (AWS)",
        position: "Partner Cloud Architect (1.5 years)",
        highlights: [
          "Designed, implemented, and optimized scalable, secure, and cost-effective AWS cloud solutions with a focus on resiliency, high availability, performance, and cost efficiency",
          "Leveraged ROSA/OpenShift, AWS Bedrock, and various other AWS services to deliver intelligent, automated solutions",
          "Collaborated with partners and customers to translate requirements into robust cloud architectures",
          "Led training sessions and workshops for internal teams and clients, focusing on Red Hat, Open-Source, and AWS architecture best practices",
          "Conducted industry events, webinars, and created customer success stories",
        ],
        summary:
          "Designed and implemented cloud solutions with a focus on resiliency, high availability, and performance while enabling partners and customers through education and best practices over a 1.5-year tenure.",
        url: "https://aws.amazon.com/",
        technologies: [
          // Core AWS Services
          "AWS",
          "EC2",
          "Lambda",
          "S3",
          "RDS",
          "DynamoDB",
          "CloudFormation",
          "CloudFront",
          "Route 53",
          "IAM",
          // Container & Orchestration
          "EKS",
          "ECS",
          "Fargate",
          "App Runner",
          "OpenShift",
          "ROSA",
          // Networking & Security
          "VPC",
          "Direct Connect",
          "WAF",
          "Shield",
          "GuardDuty",
          // DevOps & CI/CD
          "CodePipeline",
          "CodeBuild",
          "CodeDeploy",
          "CodeCommit",
          // AI/ML Services
          "SageMaker",
          "Bedrock",
          "Rekognition",
          "Lex",
          // Monitoring & Management
          "CloudWatch",
          "CloudTrail",
          "Config",
          "Trusted Advisor",
          // Hybrid Cloud
          "Outposts",
          "Storage Gateway",
          "VMware Cloud on AWS",
          // Migration
          "Application Discovery Service",
          "Database Migration Service",
          "Server Migration Service",
          // Security & Compliance
          "KMS",
          "Secrets Manager",
          "Security Hub",
          // Developer Tools
          "Cloud9",
          "X-Ray",
          "Step Functions",
          // Architecture & Best Practices
          "Cloud Architecture",
          "Open-Source",
          "Well-Architected Framework",
        ],
      },
      {
        name: "Red Hat",
        position: "Emerging Technologies Cloud Engineer (7 years)",
        highlights: [
          "Led modernization and digital transformation initiatives by driving strategic cloud adoption",
          "Integrated emerging tech: blockchain, edge, OpenShift, AI/ML, serverless",
          "Facilitated cloud-centric operating model transitions",
          "Developed tailored migration strategies and multiphase roadmaps",
          "Published technical articles and led industry conference presentations",
          "Fostered a culture of innovation and collaboration",
        ],
        summary:
          "Led cloud transformation initiatives and emerging technology integration, helping organizations modernize their infrastructure and adopt cloud-native solutions over a 7-year tenure.",
        url: "https://www.redhat.com/",
        technologies: [
          // Container & Orchestration
          "OpenShift",
          "Kubernetes",
          "Docker",
          "Podman",
          "Containerd",
          // Infrastructure as Code & Automation
          "Terraform",
          "Ansible",
          "Pulumi",
          "Crossplane",
          "ArgoCD",
          "Flux",
          // Service Mesh & Serverless
          "Istio",
          "Linkerd",
          "Knative",
          "OpenFaaS",
          "KEDA",
          // Monitoring & Observability
          "Prometheus",
          "Grafana",
          "Loki",
          "Tempo",
          "OpenTelemetry",
          // CI/CD & GitOps
          "Tekton",
          "Jenkins X",
          "Argo Workflows",
          "Tekton Pipelines",
          "Flagger",
          // Cloud Platforms
          "OpenStack",
          "Kubernetes",
          "Red Hat OpenShift",
          "OKD",
          "Minikube",
          "Kind",
          // Programming & Frameworks
          "Python",
          "Go",
          "Node.js",
          "React",
          "Next.js",
          "FastAPI",
          "Gin",
          // Open Source Projects
          "KubeVirt",
          "KubeVelo",
          "Crossplane",
          "Falco",
          "Kyverno",
          "OPA Gatekeeper",
          "KubeEdge",
          "K3s",
          "Rook",
          "Longhorn",
          "Kubernetes Operators",
          "Helm",
          "Kustomize",
          "Backstage",
          "Tilt",
          "Skaffold",
          "Tekton",
          "Argo Projects",
        ],
      },
      {
        name: "FICO",
        position: "Director of Cloud Service Platforms (2 years)",
        highlights: [
          "Led strategic planning, design, and implementation of cloud service platforms",
          "Drove digital transformation and optimized infrastructure performance",
          "Developed comprehensive cloud strategies and managed vendor relationships",
          "Ensured compliance with security, data protection, and regulatory standards",
          "Built and mentored high-performance teams",
        ],
        summary:
          "Directed the development and implementation of cloud service platforms over 2 years, driving digital transformation and operational excellence through strategic leadership and innovation.",
        url: "https://www.fico.com/",
        technologies: [
          "Strategic Leadership",
          "Team Management",
          "Budget Oversight",
          "Vendor Management",
          "Stakeholder Engagement",
          "Agile Transformation",
          "Cloud Governance",
          "Risk Management",
          "Compliance & Security",
          "Digital Strategy",
          "Technical Roadmapping",
          "Cross-functional Leadership",
          "Mentorship & Coaching",
          "Performance Management",
          "Cloud Platforms",
          "Digital Transformation",
          "Infrastructure Optimization",
          "Kubernetes",
          "Multi-Cloud",
          "AI/ML",
          "Serverless",
          "Open-Source",
          "IaC",
          "PaaS",
          "IaaS",
          "AWS Certified Solutions Architect",
          "AWS Well-Architected Framework",
          "Amazon EKS",
          "AWS Lambda",
          "Amazon RDS",
          "Amazon S3",
          "AWS IAM",
          "Amazon VPC",
          "AWS CloudFormation",
          "AWS CloudTrail",
          "Amazon CloudWatch",
          "AWS Security Hub",
          "AWS Organizations",
          "Red Hat OpenShift",
          "Red Hat Enterprise Linux (RHEL)",
          "Red Hat Ansible Automation Platform",
          "Red Hat OpenStack Platform",
          "Red Hat Quay",
          "Red Hat Advanced Cluster Management",
          "Red Hat OpenShift Container Platform",
          "Red Hat OpenShift Service Mesh",
          "Red Hat Fuse",
          "Red Hat 3scale API Management",
          "Red Hat Satellite",
          "Red Hat Identity Management",
        ],
      },
      {
        name: "American Express",
        position: "Senior Data Architect (4 years)",
        highlights: [
          "Designed and implemented scalable, performant, and secure data solutions driving data-driven insights and business value",
          "Applied data architecture principles and technologies to solve complex business challenges",
          "Crafted conceptual, logical, and physical data models ensuring data integrity and accessibility",
          "Designed big data environments (Hadoop, Spark) for high-volume, high-velocity data analysis",
          "Architected solutions empowering advanced analytics, machine learning, and real-time decision-making",
          "Demonstrated expertise in data privacy regulations, security frameworks, and access control methodologies",
        ],
        summary:
          "Led data architecture initiatives at American Express over 4 years, designing scalable solutions that enabled advanced analytics and machine learning capabilities while ensuring compliance with enterprise governance standards and regulatory requirements.",
        url: "https://www.linkedin.com/company/american-express/",
        technologies: [
          "Hadoop",
          "Spark",
          "Machine Learning",
          "Data Architecture",
          "Big Data",
        ],
      },
      {
        name: "VCE",
        position: "Principal Architect (3 years)",
        highlights: [
          "Led design and implementation of complex, innovative solutions aligning technology with business goals",
          "Architected scalable, reliable, and secure solutions across diverse domains and technologies",
          "Developed technology roadmaps and long-term architectural visions for organizational growth",
          "Designed and implemented large-scale, distributed, and highly available systems",
          "Integrated disparate systems ensuring interoperability with focus on scalability and performance",
          "Mentored teams and fostered culture of innovation while engaging stakeholders at all organizational levels",
        ],
        summary:
          "Served as Principal Architect at VCE for 3 years, leading the design of enterprise-scale converged infrastructure solutions and establishing architectural standards that enhanced system performance and organizational capabilities.",
        url: "https://www.linkedin.com/company/vce/",
        technologies: [
          "Converged Infrastructure",
          "Scalable Systems",
          "Distributed Systems",
          "System Integration",
        ],
      },
      {
        name: "Microsoft",
        position: "Senior System Engineer (3 years)",
        highlights: [
          "Designed, implemented, and managed the Microsoft CORE software development infrastructure platform",
          "Leveraged Microsoft and open-source technologies to optimize system performance and enhance security",
          "Architected scalable and resilient systems based on Microsoft technologies",
          "Implemented security controls and ensured compliance with industry standards",
          "Demonstrated expertise in capacity planning, performance tuning, and infrastructure optimization",
        ],
        summary:
          "Engineered and managed the Microsoft CORE software development infrastructure platform for 3 years, delivering scalable solutions that optimized system performance while establishing security frameworks and hybrid cloud capabilities.",
        url: "https://www.linkedin.com/company/microsoft/",
        location: "Greater Seattle Area",
        technologies: [
          "Microsoft Azure",
          "Windows Server",
          "Active Directory",
          "Hybrid Cloud",
        ],
      },
    ],
    volunteer: [
      {
        organization: "Humane Society",
        position: "Volunteer",
        startDate: "2010-01-01",
        current: true,
        summary:
          "Volunteer work supporting animal welfare and community outreach programs.",
        highlights: [
          "Supported animal welfare initiatives and community outreach programs",
          "Contributed to fundraising and awareness campaigns",
          "Assisted with animal care and adoption events",
        ],
      },
    ],
    skills: [
      {
        name: "Cloud Platforms & Infrastructure",
        keywords: [
          "Amazon Web Services (AWS)",
          "Microsoft Azure",
          "Google Cloud Platform (GCP)",
          "Red Hat OpenStack",
          "Red Hat OpenShift",
          "Amazon EKS",
          "Google Kubernetes Engine (GKE)",
          "IBM Cloud",
          "VMware vSphere",
          "Hybrid Cloud Architecture",
          "Multi-Cloud Strategy",
          "Private Cloud",
          "Infrastructure as a Service (IaaS)",
          "Platform as a Service (PaaS)",
          "AWS Lambda",
          "AWS ECS",
          "AWS Fargate",
          "AWS S3",
          "AWS RDS",
          "AWS EBS",
          "AWS EFS",
          "AWS IAM",
          "AWS VPC",
          "AWS Direct Connect",
          "AWS Transit Gateway",
          "Azure Functions",
          "Azure Kubernetes Service",
          "Azure Blob Storage",
          "Azure SQL",
          "Google Cloud Functions",
          "Cloud Run",
          "Cloud SQL",
        ],
      },
      {
        name: "DevOps & Cloud Native",
        keywords: [
          "Kubernetes",
          "Docker",
          "Containers",
          "Pods",
          "Operators",
          "Helm",
          "Kustomize",
          "OpenShift",
          "Amazon EKS",
          "Google Kubernetes Engine (GKE)",
          "AKS",
          "Rancher",
          "GitOps",
          "CI/CD",
          "DevOps",
          "ArgoCD",
          "Jenkins",
          "GitHub Actions",
          "GitLab CI/CD",
          "Tekton",
          "Flux",
          "CircleCI",
          "Travis CI",
          "Spinnaker",
          "Terraform",
          "AWS CloudFormation",
          "Azure Resource Manager",
          "Pulumi",
          "Crossplane",
          "Ansible",
          "Configuration Management",
          "Infrastructure as Code (IaC)",
        ],
      },
      {
        name: "Monitoring & Observability",
        keywords: [
          "Prometheus",
          "Grafana",
          "ELK Stack",
          "OpenTelemetry",
          "Jaeger",
          "New Relic",
          "Datadog",
          "Splunk",
          "Dynatrace",
          "Loki",
          "Thanos",
          "Monitoring",
          "Alerting",
          "Log Management",
          "Distributed Tracing",
          "Metrics Collection",
          "Performance Monitoring",
        ],
      },
      {
        name: "Development & Architecture",
        keywords: [
          "Python",
          "Go",
          "Java",
          "JavaScript",
          "Node.js",
          "React",
          "TypeScript",
          "Microservices Architecture",
          "API Design & Development",
          "REST",
          "gRPC",
          "GraphQL",
          "Event-Driven Architecture",
          "Serverless Computing",
          "Database Design",
          "System Integration",
          "Distributed Systems",
          "Service Mesh",
          "API Gateway",
          "Service Discovery",
          "Circuit Breaker",
          "CQRS",
          "Event Sourcing",
          "Saga Pattern",
          "WebSockets",
        ],
      },
      {
        name: "AI/ML & Emerging Technologies",
        keywords: [
          "Machine Learning",
          "Artificial Intelligence",
          "Large Language Models (LLMs)",
          "AWS Bedrock",
          "TensorFlow",
          "PyTorch",
          "Keras",
          "scikit-learn",
          "MLflow",
          "Kubeflow",
          "SageMaker",
          "Vertex AI",
          "Azure ML",
          "Hugging Face",
          "LangChain",
          "Deep Learning",
          "Neural Networks",
          "Computer Vision",
          "Natural Language Processing (NLP)",
          "Generative AI",
          "Model Serving",
          "MLOps",
          "Data Analytics",
          "IoT Solutions",
          "Edge Computing",
          "Blockchain Technology",
        ],
      },
      {
        name: "Security & Compliance",
        keywords: [
          "Zero Trust Architecture",
          "mTLS",
          "SPIFFE/SPIRE",
          "Vault",
          "Keycloak",
          "OAuth2",
          "OpenID Connect",
          "Istio Security",
          "OPA Gatekeeper",
          "Falco",
          "Trivy",
          "Aqua Security",
          "Cloud Security",
          "Identity and Access Management",
          "Security Frameworks",
          "Compliance",
          "Data Protection",
          "Regulatory Standards",
        ],
      },
      {
        name: "Data & Storage",
        keywords: [
          "Data Architecture",
          "Data Mesh",
          "Data Fabric",
          "Data Lake",
          "Data Warehouse",
          "Data Lakehouse",
          "ETL/ELT",
          "Apache Kafka",
          "Apache Spark",
          "Presto",
          "Hadoop",
          "Stream Processing",
          "Batch Processing",
          "Real-time Analytics",
          "Time-series Data",
          "Graph Databases",
          "Vector Databases",
          "Data Governance",
          "Master Data Management",
        ],
      },
      {
        name: "Professional & Leadership",
        keywords: [
          "Technical Leadership",
          "Solution Architecture",
          "Enterprise Architecture",
          "Digital Transformation",
          "Agile Methodologies",
          "Scrum",
          "Kanban",
          "SAFe",
          "DevOps Culture",
          "Site Reliability Engineering (SRE)",
          "Team Leadership",
          "Project Management",
          "Stakeholder Management",
          "Customer Success",
          "Partner Enablement",
          "Training and Development",
          "Mentoring",
          "Coaching",
          "Technical Writing",
          "Public Speaking",
          "Workshop Facilitation",
          "Community Building",
          "Open Source Contribution",
          "Technical Evangelism",
          "Thought Leadership",
          "Business Development",
          "Pre-sales Engineering",
        ],
      },
    ],
    certificates: [
      // AI/ML Certifications
      {
        name: "Build Rich-Context AI Apps (MCP)",
        issuer: "Anthropic",
        startDate: "2024-01-01",
        url: "https://www.anthropic.com/certification/mcp",
      },
      {
        name: "Agent Communication Protocol (ACP)",
        issuer: "DeepLearning.AI",
        startDate: "2024-01-01",
        url: "https://www.deeplearning.ai/short-courses/agent-communication-protocol/",
      },
      // Amazon Certifications
      {
        name: "AWS Certified Solutions Architect",
        issuer: "Amazon Web Services (AWS)",
        endDate: "2025-02-28",
        startDate: "2023-02-28",
        url: "https://www.credly.com/badges/3f30bd86-6157-4c5d-9ff5-5b47f38cdb07/public_url",
      },
      // Google Certifications
      {
        name: "Google Cloud - Generative AI Leader",
        issuer: "Google",
        startDate: "2023-01-01",
        url: "https://www.cloudskillsboost.google/public_profiles/7f9c8f2d-8b1a-4e1a-9e1a-1a1a1a1a1a1a/certifications/generative-ai-leader",
      },
      {
        name: "Google Cloud - Introduction to Generative AI",
        issuer: "Google",
        startDate: "2023-06-30",
        url: "https://cdn.qwiklabs.com/%2FTntrCzBhpKkF9LHUgvevvIKQb2%2Bufpupa1zPSlY%2Fcs%3D",
      },
      // Red Hat Certifications
      {
        name: "Red Hat Certificate of Expertise in Platform-as-a-Service",
        issuer: "Red Hat",
      },
      {
        name: "Red Hat Certificate of Expertise in Data Virtualization",
        issuer: "Red Hat",
      },
      {
        name: "Red Hat Certificate of Expertise in Clustering and Storage Management",
        issuer: "Red Hat",
      },
      // VMware Certifications
      {
        name: "VMware Certified Professional - Data Center Virtualization",
        issuer: "VMware",
      },
      {
        name: "VMware Sales Professional, Application Modernization, Data Management, Business Continuity, Virtualization of Business Critical Applications, Management, Cloud IaaS, Desktop Virtualization",
        issuer: "VMware",
      },
      {
        name: "VMware Technical Sales Professional - Business Continuity, Virtualization of Business Critical Applications, Data Management, Infrastructure Virtualization",
        issuer: "VMware",
      },
      {
        name: "VMware Certified Associate - Data Center Virtualization, Cloud, Workforce Mobility",
        issuer: "VMware",
      },
      // VCE Certifications
      {
        name: "Certified to architect, deploy and implement Vblock 100/200/300/700 infrastructure systems",
        issuer: "VCE",
      },
    ],
    publications: [
      {
        name: "DELL-EMC MERGER LEAVES IT PROS HOPEFUL AND CONCERNED",
        publisher: "TechTarget SearchStorage",
        releaseDate: "2015-12-31",
        url: "https://www.techtarget.com/searchstorage/feature/A-year-later-Pros-and-cons-of-the-Dell-and-EMC-merger",
      },
      {
        name: "FICO CHOOSES RED HAT TO DEPLOY OPENSTACK, MANAGEMENT, AND STORAGE SOLUTIONS FOR AGILE CLOUD INFRASTRUCTURE",
        publisher: "Red Hat Press Release",
        releaseDate: "2015-10-26",
        url: "https://www.redhat.com/en/about/press-releases/fico-chooses-red-hat-deploy-openstack-management-and-storage-solutions-agile-cloud-infrastructure",
      },
      {
        name: "WHAT'S BEHIND THE ODD COUPLE MICROSOFT-RED HAT PARTNERSHIP",
        publisher: "RCP Magazine",
        releaseDate: "2015-11-04",
        url: "https://rcpmag.com/articles/2015/11/04/microsoft-red-hat-put-linux-on-azure.aspx",
      },
      {
        name: "WILL OPEN SOURCE STORAGE MAKE THE HYPER SCALE DREAM REAL?",
        publisher: "The Register",
        releaseDate: "2015-11-09",
        url: "https://www.theregister.com/2015/11/09/open_source_hyperscale_storage/",
      },
      {
        name: "FICO EMBRACES OPENSTACK",
        publisher: "SiliconANGLE",
        releaseDate: "2015-08-21",
        url: "https://www.youtube.com/watch?v=NxOXdeOrfJE",
      },
      {
        name: "FICO PROVES THE MAINSTREAM OPENSTACK ADOPTION POINT",
        publisher: "Forbes",
        releaseDate: "2015-06-09",
        url: "https://www.forbes.com/sites/benkepes/2015/06/09/fico-proves-the-mainstream-openstack-adoption-point/",
      },
      {
        name: "FICO SAYS OPENSTACK ENTERPRISE IS READY FOR PRIMETIME",
        publisher: "TechTarget SearchCloudComputing",
        releaseDate: "2015-05-01",
        url: "https://www.techtarget.com/searchcloudcomputing/tip/Five-challenges-with-open-source-cloud-infrastructure-tools",
      },
      {
        name: "HOW STORAGE WORKS IN CONTAINERS",
        publisher: "OpenStack Superuser",
        releaseDate: "2016-09-09",
        url: "https://superuser.openinfra.org/articles/how-storage-works-in-containers/",
      },
      {
        name: "HOW TO BUILD A LARGE SCALE MULTI-TENANT CLOUD SOLUTION",
        publisher: "LinkedIn",
        releaseDate: "2015-03-01",
        url: "https://www.linkedin.com/pulse/how-build-large-scale-multi-tenant-cloud-solution-gerasimatos",
      },
      {
        name: "THINK FICO IS A CREDIT SCORING COMPANY? NOPE: IT'S ABOUT LARGE-SCALE ANALYTICS",
        publisher: "OpenStack Superuser",
        releaseDate: "2015-02-01",
        url: "https://superuser.openinfra.org/articles/think-fico-is-a-credit-scoring-company-nope-it-s-about-large-scale-analytics/",
      },
    ],
    awards: [
      {
        title:
          "RED HAT HONORS OUTSTANDING ACHIEVEMENTS IN OPEN SOURCE WITH NINTH ANNUAL RED HAT INNOVATION AWARDS",
        date: "2015-10-26",
        awarder: "Red Hat",
        summary:
          "Recognizing striking flexibility, scalability, cost effectiveness, performance, and security within an infrastructure. Winner: FICO - Leading analytics software company FICO developed FICO® Analytic Cloud using OpenShift Enterprise, driving more than US$10 million in sales in a short time frame, reducing infrastructure operations staffing by 20 percent, saving hundreds of thousands of dollars in hardware costs, and improving time to market by 70 percent.",
        url: "http://www.marketwatch.com/story/fico-chooses-red-hat-to-deploy-openstack-management-and-storage-solutions-for-agile-cloud-infrastructure-2015-10-26",
      },
    ],
    references: [
      {
        name: "David Mitrany",
        reference:
          "Nicholas Gerasimatos is a very bright individual in high demand due to his extensive leadership and problem solving skills. You only need to glance at the list of high profile companies Nicholas has worked for - which is impressive in itself but once you interview Nicholas, that is when you realize Nicholas is the real deal.",
      },
      {
        name: "Al Eskew",
        reference:
          "I had the opportunity and privilege of working with Nick during his time with Amex. His technical expertise is of the highest caliber and I would highly recommend him in any of his listed skill sets.",
      },
      {
        name: "Tony Peters",
        reference:
          "I have had the opportunity to work with Nick at FICO for the past two years designing the architecture of FICO's cloud infrastructure. There are few people out in the industry that equal Nick's combined knowledge of Cloud, Virtualization, Compute and Storage. The most impressive part of working with Nick is not just that he is knowledgeable but more importantly he's an outstanding communicator and leader, both for the teams he represents as well as working with FICO's business partners, which I am fortunate to take part in.",
      },
      {
        name: "Todd N Marinich",
        reference:
          "I had pleasure to have Nicholas on our team at AMEX. Nicholas is a dedicated technologist and trusted advisor in his field. His dedication and commitment to his craft is very impressive. Always striving to achieve the client's goals and a team player. I would recommend Nicholas to anyone looking to solve challenging objectives.",
      },
      {
        name: "Justin Watson",
        reference:
          "Nick is one of the most talented professionals I have had the honor to work with in technology. He came in to address performance issues with the virtual environment and a mis-configured UCS system. Nick hit the ground running, extremely knowledgeable and confident. He proposed bold changes and produced big results very quickly. He is a subject matter expert across many disciples and continues to embrace emerging technologies.",
      },
      {
        name: "Kyle Bardet",
        reference:
          "I had the pleasure of working with Nick during his Residency at PayPal for VCE. Nick was instrumental in the initial implementation of the Vblock infrastructure, as well as leading the Managed Services team that was onsite. Nick is the consummate professional and worked very closely with the customer, understanding their needs and offering direction, when required. He worked well with the other team members to ensure the highest level of customer satisfaction. He brought a lot to the table on the technical side, applying his vast experience to this new implementation. Nick is a hard worker and will stay with an issue until it becomes fully resolved, no matter the timeframe or the effort required.",
      },
    ],
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {resumeData.basics.name}
              </h1>
              <p className="text-gray-600">{resumeData.basics.label}</p>
            </div>
            <div className="flex items-center gap-4">
              <ResumeDownload resumeData={resumeData} />
              <Button variant="outline" size="sm" asChild>
                <a
                  href="https://github.com/nicholasg-dev"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Github className="h-4 w-4 mr-2" />
                  GitHub
                </a>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <a
                  href={resumeData.basics.profiles.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Linkedin className="h-4 w-4 mr-2" />
                  LinkedIn
                </a>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <HeroSection resumeData={resumeData} />

      {/* Navigation Tabs */}
      <section className="pb-16 px-6">
        <div className="container mx-auto max-w-6xl">
          <NavigationTabs
            activeSection={activeSection}
            setActiveSection={setActiveSection}
          />
        </div>
      </section>

      {/* Content Sections */}
      <section className="pb-16 px-6">
        <div className="container mx-auto max-w-6xl">
          {activeSection === "experience" && (
            <ExperienceSection work={resumeData.work} />
          )}
          {activeSection === "skills" && (
            <SkillsSection skills={resumeData.skills} />
          )}
          {activeSection === "projects" && (
            <div className="animate-in fade-in duration-500">
              <FeaturedProjects />
            </div>
          )}
          {activeSection === "publications" && (
            <PublicationsSection
              awards={resumeData.awards}
              publications={resumeData.publications}
            />
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-6">
        <div className="container mx-auto max-w-6xl text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Connect?</h3>
          <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
            I'm always interested in discussing innovative cloud solutions,
            emerging technologies, and opportunities to drive digital
            transformation.
          </p>
          <div className="flex justify-center">
            <Button asChild size="lg">
              <a
                href={resumeData.basics.profiles.linkedin}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin className="h-5 w-5 mr-2" />
                Connect on LinkedIn
              </a>
            </Button>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-gray-500 text-sm">
            <p>
              © 2024 {resumeData.basics.name}. Built with modern web
              technologies.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
