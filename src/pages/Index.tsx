import { useState } from "react";
// import Image from "next/image"; // Unused, and this is not a Next.js project
import { <PERSON><PERSON> } from "@/components/ui/button";
import { /* Mail, */ Linkedin, Github, AlertCircle, Loader2 } from "lucide-react"; // Mail is unused
import { Alert, AlertDescription } from "@/components/ui/alert";
import FeaturedProjects from "@/components/FeaturedProjects";
import HeroSection from "./HeroSection";
import ExperienceSection from "./ExperienceSection";
import SkillsSection from "./SkillsSection";
import PublicationsSection from "./PublicationsSection";
import NavigationTabs from "./NavigationTabs";
import ResumeDownload from "@/components/ResumeDownload";
import { useResumeData } from "@/hooks/useMarkdownResume";

const Index = () => {
  const [activeSection, setActiveSection] = useState("experience");
  const { data: resumeData, loading, error, refetch } = useResumeData();

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading resume data...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Error Alert */}
      {error && (
        <Alert className="mx-6 mt-4 border-orange-200 bg-orange-50">
          <AlertCircle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            Failed to load resume data from markdown file. Using fallback data.
            <Button
              variant="link"
              size="sm"
              onClick={refetch}
              className="ml-2 h-auto p-0 text-orange-600 underline"
            >
              Try again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {resumeData.basics.name}
              </h1>
              <p className="text-gray-600">{resumeData.basics.label}</p>
            </div>
            <div className="flex items-center gap-4">
              <ResumeDownload resumeData={resumeData} />
              <Button variant="outline" size="sm" asChild>
                <a
                  href="https://github.com/nicholasg-dev"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Github className="h-4 w-4 mr-2" />
                  GitHub
                </a>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <a
                  href={resumeData.basics.profiles.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Linkedin className="h-4 w-4 mr-2" />
                  LinkedIn
                </a>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <HeroSection resumeData={resumeData} />

      {/* Navigation Tabs */}
      <section className="pb-16 px-6">
        <div className="container mx-auto max-w-6xl">
          <NavigationTabs
            activeSection={activeSection}
            setActiveSection={setActiveSection}
          />
        </div>
      </section>

      {/* Content Sections */}
      <section className="pb-16 px-6">
        <div className="container mx-auto max-w-6xl">
          {activeSection === "experience" && (
            <ExperienceSection work={resumeData.work} />
          )}
          {activeSection === "skills" && (
            <SkillsSection skills={resumeData.skills} />
          )}
          {activeSection === "projects" && (
            <div className="animate-in fade-in duration-500">
              <FeaturedProjects />
            </div>
          )}
          {activeSection === "publications" && (
            <PublicationsSection
              awards={resumeData.awards}
              publications={resumeData.publications}
            />
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-6">
        <div className="container mx-auto max-w-6xl text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Connect?</h3>
          <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
            I'm always interested in discussing innovative cloud solutions,
            emerging technologies, and opportunities to drive digital
            transformation.
          </p>
          <div className="flex justify-center">
            <Button asChild size="lg">
              <a
                href={resumeData.basics.profiles.linkedin}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin className="h-5 w-5 mr-2" />
                Connect on LinkedIn
              </a>
            </Button>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-gray-500 text-sm">
            <p>
              © 2024 {resumeData.basics.name}. Built with modern web
              technologies.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
