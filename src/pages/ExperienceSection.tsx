import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
// import { Calendar } from "lucide-react"; // Unused import

interface Job {
  name: string;
  position: string;
  startDate?: string;
  endDate?: string;
  current?: boolean;
  highlights: string[];
  technologies: string[];
  logo?: string;
  url?: string;
}

interface ExperienceSectionProps {
  work: Job[];
}

const ExperienceSection = ({ work }: ExperienceSectionProps) => {
  // Unused function:
  // const extractDuration = (position: string) => {
  //   const match = position.match(
  //     /\((\d+(\.\d+)?\s*(?:years?|yrs?|mos?)|\d+\+?\s*(?:years?|yrs?|mos?))(?:\s*\d*\s*(?:months?|mos?))?\s*\)/i,
  //   );
  //   return match ? match[0] : "";
  // };

  // Helper function to get the correct logo path based on company name
  const getLogoPath = (companyName: string) => {
    const logoMap: { [key: string]: string } = {
      AHEAD:
        "https://www.servicenow.com/content/dam/servicenow-assets/public/en-us/digital-graphics/ds-logos/logo-ahead.svg",
      "Amazon Web Services (AWS)":
        "https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg",
      "Red Hat":
        "https://upload.wikimedia.org/wikipedia/commons/d/d8/Red_Hat_logo.svg",
      FICO: "https://upload.wikimedia.org/wikipedia/commons/9/9a/FICO_logo.svg",
      "American Express":
        "https://upload.wikimedia.org/wikipedia/commons/f/fa/American_Express_logo_%282018%29.svg",
      VCE: "https://www.logo.wine/a/logo/VCE_(company)/VCE_(company)-Logo.wine.svg",
      Microsoft:
        "https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg",
    };

    return logoMap[companyName] || "/placeholder.svg";
  };

  return (
    <div className="space-y-8 animate-in fade-in duration-500">
      <h3 className="text-3xl font-bold text-center mb-12">
        Professional Experience
      </h3>
      {work.map((job, index) => (
        <Card key={index} className="p-8 hover:shadow-lg transition-shadow">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-6">
            <div className="flex items-start gap-4 mb-4 md:mb-0">
              <div className="flex-shrink-0 w-12 h-12 bg-white rounded-lg border border-gray-200 flex items-center justify-center p-1">
                <img
                  src={job.logo || getLogoPath(job.name)}
                  alt={`${job.name} logo`}
                  className="w-10 h-10 object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    // Fallback to placeholder if image fails to load
                    target.src = "/placeholder.svg";
                  }}
                  loading="lazy"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h4 className="text-xl font-semibold text-gray-900">
                    {job.position.replace(/\([^)]*\)/g, "").trim()}
                    {job.current && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Current
                      </span>
                    )}
                  </h4>
                </div>
                <h5 className="text-lg font-medium text-blue-600 mb-2">
                  {job.url ? (
                    <a
                      href={job.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:underline"
                    >
                      {job.name}
                    </a>
                  ) : (
                    job.name
                  )}
                </h5>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <ul className="space-y-2">
              {job.highlights.map((highlight, i) => (
                <li key={i} className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-700 leading-relaxed">
                    {highlight}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex flex-wrap gap-2">
            {job.technologies.map((tech, i) => (
              <Badge
                key={i}
                variant="secondary"
                className="bg-blue-50 text-blue-700 hover:bg-blue-100"
              >
                {tech}
              </Badge>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
};

export default ExperienceSection;
