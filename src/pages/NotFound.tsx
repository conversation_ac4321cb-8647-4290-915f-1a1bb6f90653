import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname,
    );

    // Auto-redirect after 10 seconds
    const timer = setInterval(() => {
      setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    const redirectTimer = setTimeout(() => {
      navigate("/");
    }, 10000);

    return () => {
      clearInterval(timer);
      clearTimeout(redirectTimer);
    };
  }, [location.pathname, navigate]);

  const asciiArt = `
   ___   _   ___  ___ ___   _     _      _   _  ___ 
  | __| /_\\ | _ / __| _ \\ /_\\   | |    /_\\ | | | |/ __|
  | _| / _ \\|   / (_ |   // _ \\  | |__ / _ \\| |_| | (_ |
  |___/_/ \\_\\_|_\\___|_|_/_/ \\_\\ |____/_/ \\_\\___/ \\___|
  `;

  const errorMessages = [
    "The page you're looking for has left the atmosphere.",
    "This page is in another castle!",
    "404: Page Not Found - It's dangerous to go alone! Take this link home.",
    "The page you seek is not in this galaxy.",
    "Error 404: The page has been abducted by aliens!",
  ];

  const randomMessage =
    errorMessages[Math.floor(Math.random() * errorMessages.length)];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 text-white p-4">
      <div className="text-center max-w-4xl mx-auto p-8 rounded-xl bg-gray-900/50 backdrop-blur-sm border border-purple-500/20 shadow-2xl">
        <pre className="text-green-400 font-mono text-xs md:text-sm mb-6 overflow-x-auto">
          {asciiArt}
        </pre>

        <div
          className="mb-6
          bg-gradient-to-r from-purple-500 to-pink-500 
          bg-clip-text text-transparent 
          text-4xl md:text-6xl font-bold"
        >
          SYSTEM ERROR 404
        </div>

        <p className="text-xl text-gray-300 mb-8">{randomMessage}</p>

        <div className="mb-8 p-4 bg-gray-800/50 rounded-lg border border-purple-500/30">
          <p className="text-yellow-400 font-mono text-sm mb-2">$ cd ~/home</p>
          <p className="font-mono text-sm">
            <span className="text-green-400">$</span> Redirecting in{" "}
            <span className="text-purple-400">{countdown}s</span>...
          </p>
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-4 mt-8">
          <button
            onClick={() => navigate(-1)}
            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors duration-200"
          >
            ← Go Back
          </button>
          <button
            onClick={() => navigate("/")}
            className="px-6 py-3 bg-pink-600 hover:bg-pink-700 rounded-lg font-medium transition-colors duration-200"
          >
            🚀 Return to Home
          </button>
        </div>

        <div className="mt-10 text-xs text-gray-500">
          <p>// Error details for nerds: {location.pathname} not found</p>
          <p className="mt-2">// May the source be with you!</p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
