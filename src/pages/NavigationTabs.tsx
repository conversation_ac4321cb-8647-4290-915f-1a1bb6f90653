import React from "react";
import { Button } from "@/components/ui/button";
import { Briefcase, Code, Folder, /* Award, */ BookOpen } from "lucide-react"; // Award is unused

interface NavigationTabsProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
}

const NavigationTabs = ({
  activeSection,
  setActiveSection,
}: NavigationTabsProps) => {
  const tabs = [
    { id: "experience", label: "Experience", icon: Briefcase },
    { id: "skills", label: "Skills", icon: Code },
    { id: "projects", label: "Featured Projects", icon: Folder },
    { id: "publications", label: "Publications", icon: BookOpen },
  ];

  return (
    <div className="flex flex-wrap justify-center gap-2 mb-8">
      {tabs.map(({ id, label, icon: Icon }) => (
        <Button
          key={id}
          variant={activeSection === id ? "default" : "outline"}
          onClick={() => setActiveSection(id)}
          className="flex items-center gap-2"
        >
          <Icon className="h-4 w-4" />
          {label}
        </Button>
      ))}
    </div>
  );
};

export default NavigationTabs;
