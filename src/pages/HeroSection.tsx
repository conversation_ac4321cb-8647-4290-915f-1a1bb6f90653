import React from "react";
// import { Card } from "@/components/ui/card"; // Unused
// import { Button } from "@/components/ui/button"; // Unused
// import { Linkedin, Mail } from "lucide-react"; // Unused

interface HeroSectionProps {
  resumeData: {
    basics: {
      name: string;
      label: string;
      summary: string;
      location: string;
      email?: string;
      profiles: {
        linkedin: string;
        github?: string;
      };
    };
  };
}

const HeroSection = ({ resumeData }: HeroSectionProps) => {
  return (
    <section className="py-16 px-6">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-12">
          {/* Profile Image */}
          <div className="mb-8">
            <img
              src="/selfie.png"
              alt="Nicholas Gerasimatos"
              className="w-40 h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 rounded-full mx-auto object-cover shadow-lg border-4 border-white"
            />
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            <div>Principal Technical Consultant</div>
            <div className="text-blue-600">Cloud and Platform Engineering</div>
          </h2>
          <p className="text-base text-gray-600 max-w-4xl mx-auto leading-normal">
            {resumeData.basics.summary}
          </p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
